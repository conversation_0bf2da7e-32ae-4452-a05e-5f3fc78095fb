<!-- Routing configuration for Microsoft IIS web server -->
<configuration>
    <system.webServer>
        <security>
            <requestFiltering>
                <hiddenSegments>
                    <add segment=".env" />
                    <add segment="silverstripe-cache" />
                    <add segment="composer.json" />
                    <add segment="composer.lock" />
                </hiddenSegments>
                <fileExtensions allowUnlisted="true" >
                    <add fileExtension=".ss" allowed="false"/>
                    <add fileExtension=".yml" allowed="false"/>
                </fileExtensions>
            </requestFiltering>
        </security>
        <rewrite>
            <rules>
                <rule name="SilverStripe Clean URLs" stopProcessing="true">
                    <match url="^(.*)$" />
                    <conditions>
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="index.php" appendQueryString="true" />
                </rule>
            </rules>
        </rewrite>
    </system.webServer>
</configuration>
