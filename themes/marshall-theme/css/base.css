/**
 * Base styles
 *
 * Global styles and resets
 * Use with caution as these affect everything on the site
 */
:root {
    /* Set transition variables */
    --transition-default: all 0.2s ease-in;
    --transition-color: background-color 0.2s ease-in, border-color 0.2s ease-in, color 0.2s ease-in;
    --transition-svg: fill 0.2s ease-in;
}

*,
::before,
::after {
    box-sizing: border-box;
}

* {
    padding: 0;
    margin: 0;

    &:focus-visible {
        outline: currentcolor dashed 0.1rem;
        outline-offset: 0.2rem;
        border-radius: 0.2rem;
    }
}

html,
body {
    scroll-behavior: smooth;

    @media (prefers-reduced-motion) {
        scroll-behavior: unset;
    }
}

a {
    color: inherit;
    cursor: pointer;
    text-decoration: none;
    transition: var(--transition-color);
}

abbr[title] {
    text-decoration: underline dotted;
}

b,
strong {
    font-weight: var(--font-weight-semibold);
}

button {
    background: unset;
    border: unset;
    cursor: pointer;
    text-align: left;
    text-transform: none;
}

hr {
    height: 0;
    border: unset;
    color: inherit;
}

img {
    display: block;
    height: auto;
    max-width: 100%;
}

small {
    display: inline-block;
    font-size: var(--font-size-small);
}

sub,
sup {
    font-size: var(--font-size-small);
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}

sub {
    bottom: -0.25rem;
}

sup {
    top: -0.5rem;
}

svg {
    transition: var(--transition-svg);
}

table {
    border-collapse: collapse;
    border-color: inherit;
    text-indent: 0;

    td {
        vertical-align: top;
    }
}
