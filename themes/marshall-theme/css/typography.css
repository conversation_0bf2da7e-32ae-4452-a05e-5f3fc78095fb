/**
 * Base typography styles
 *
 * Font size is set to 10px for easy conversion of px to rem
 * e.g. 18px = 1.8rem
 */
html {
    font-size: 10px;
}

body,
button,
code,
input,
optgroup,
select,
textarea {
    color: var(--color-charcoal);
    font-family: var(--font-family-base);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-base);
    line-height: var(--line-height-base);

    @media (min-width: 992px) {
        font-size: var(--font-size-large);
    }
}

hr {
    border-bottom: 0.1rem solid var(--color-line-grey);
    margin: 1.5rem 0;
}

ol {
    list-style: decimal;
}

ul {
    list-style: disc;
}

li {
    margin-left: 1.75em;
}

code {
    display: inline-block;
    background-color: var(--color-pale-grey);
    border-radius: 0.4rem;
    box-decoration-break: clone;
    -webkit-box-decoration-break: clone;
    font-weight: var(--font-weight-medium);
    padding: 0.1rem 0.4rem;
}

/* Headings */
h1, .h1 {
    font-size: 3.8rem;
    font-weight: var(--font-weight-extrabold);
    line-height: 1.2;
    margin: 0.25em 0 0.5em;

    @media (min-width: 750px) {
        font-size: 4.8rem;
    }
}

h2, .h2 {
    font-size: 3.2rem;
    font-weight: var(--font-weight-extrabold);
    line-height: 1.2;
}

h3, .h3 {
    font-size: 2.2rem;
    font-weight: var(--font-weight-bold);
    line-height: 1.3;
}

h4, .h4 {
    font-size: 2rem;
    font-weight: var(--font-weight-bold);
    line-height: 1.3;
}

h5, .h5 {
    font-size: 1.8rem;
    font-weight: var(--font-weight-bold);
    line-height: 1.3;
}

h2, h3, h4, h5, h6,
.h2, .h3, .h4, .h5, .h6 {
    margin: 0.75em 0 0.5em;
}

/* Text */
blockquote {
    border-left: 1rem solid var(--color-charcoal);
    font-size: 2.2rem;
    font-weight: var(--font-weight-medium);
    line-height: 1.4;
    padding-left: 3rem;
}

.intro {
    font-size: 2.2rem;
}

p {
    margin-bottom: 1.25rem;
}

.text-left, .left {
    text-align: left;
}

.text-center, .center {
    text-align: center;
}

.text-right, .right {
    text-align: right;
}

.text-justify {
    text-justify: auto;
}

/* Links */
a {
    text-decoration: underline;
    text-decoration-thickness: 0.1rem;
    text-underline-offset: 20%;
}

a:hover {
    text-decoration-thickness: 0.2rem;
}

/* Tables */
table {
    border-collapse: collapse;
    border: 1px solid #d4d4d4;
    border-spacing: 0;
    margin: 0 0 10px;
    text-align: left;

    tr:nth-child(even) {
        background-color: #ededed
    }

    tr.even,
    th,
    thead td {
        background-color: #ededed
    }

    td,
    th {
        padding: 2px 5px;
        border: 1px solid #d4d4d4;
        vertical-align: top;
    }

    th {
        font-weight: bold;
    }
}

/* Images */
img {
    &.left {
        float: left;
        margin: 0 2.5rem 2.5rem 0;
    }

    &.right {
        float: right;
        margin: 0 0 2.5rem 2.5rem;
        clear: none;
    }

    &.center {
        margin: 2.5rem auto;
    }

    &.rightAlone {
        float: right;
        margin: 2.5rem 0;
        clear: both;
    }

    &.leftAlone {
        margin: 2.5rem 0;
    }

    + .caption {
        position: relative;
        top: -1.5rem;
    }
}

/* Captions */
.caption {
    &.leftAlone,
    &.left {
        text-align: left;
    }

    &.rightAlone,
    &.right {
        text-align: right;
    }

    &.center {
        text-align: center;
    }
}

/* Image with captions */
.captionImage {
    &.leftAlone {
        float: left;
        clear: both;
        margin-right: 100%;
    }

    &.rightAlone {
        float: right;
        clear: both;
        margin-left: 100%;
    }


    &.center{
        margin: 0 auto 20px;
    }

    p {
        clear: both;
        margin: 5px 0;
        font-style: italic;
    }

    p.caption.text-center {
        text-align: center;
    }

    p.caption.text-left {
        text-align: left;
    }
}
